package com.cdkit.modules.cm.application.budget;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;
import com.cdkit.modules.cm.domain.budget.repository.CenterCostImportRepository;
import com.cdkit.modules.cm.domain.budget.service.AnnualBudgetDetailDomainService;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostBudgetSubjectEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostBudgetSubjectRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;

/**
 * 年度总预算应用服务
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualBudgetApplication {

    private final CostAnnualBudgetRepository costAnnualBudgetRepository;
    private final CostAnnualBudgetDetailRepository costAnnualBudgetDetailRepository;
    private final AnnualBudgetDetailDomainService annualBudgetDetailDomainService;
    private final CostBudgetSubjectRepository costBudgetSubjectRepository;
    private final CostProjectPlanRepository costProjectPlanRepository;
    private final CenterCostImportRepository centerCostImportRepository;

    /**
     * 分页查询年度总预算列表
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostAnnualBudgetEntity> queryPageList(CostAnnualBudgetEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costAnnualBudgetRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询年度总预算详情
     *
     * @param id 年度总预算ID
     * @return 年度总预算详情
     */
    public CostAnnualBudgetEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }
        return costAnnualBudgetRepository.findById(id);
    }

    /**
     * 根据ID查询年度总预算详情（包含明细数据）
     *
     * @param id 年度总预算ID
     * @return 年度总预算详情和明细数据
     */
    public CostAnnualBudgetEntity queryDetailById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }

        // 查询主表数据
        CostAnnualBudgetEntity entity = costAnnualBudgetRepository.findById(id);
        if (entity == null) {
            return null;
        }

        // 查询明细数据
        List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList =
            costAnnualBudgetDetailRepository.findByBudgetId(id);

        log.info("查询年度总预算详情成功，ID: {}, 预算编号: {}, 明细数量: {}",
                id, entity.getBudgetCode(), budgetDetailList != null ? budgetDetailList.size() : 0);

        return entity;
    }

    /**
     * 根据ID查询年度总预算明细数据
     *
     * @param id 年度总预算ID
     * @return 明细数据列表
     */
    public List<CostAnnualBudgetEntity.BudgetDetailInfo> queryBudgetDetailsByBudgetId(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }

        return costAnnualBudgetDetailRepository.findByBudgetId(id);
    }

    /**
     * 新增年度总预算
     *
     * @param entity 年度总预算实体
     * @return 年度总预算ID
     */
    public String add(CostAnnualBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("年度总预算数据不能为空");
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证预算编号唯一性
        if (StringUtils.hasText(entity.getBudgetCode())) {
            CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findByBudgetCode(entity.getBudgetCode());
            if (existingEntity != null) {
                throw new IllegalArgumentException("预算编号已存在：" + entity.getBudgetCode());
            }
        }

        // 计算剩余金额
        entity.calculateRevenueRemainingAmount();
        entity.calculateDirectCostRemainingAmount();

        CostAnnualBudgetEntity savedEntity = costAnnualBudgetRepository.save(entity);
        log.info("新增年度总预算成功，ID: {}, 预算编号: {}", savedEntity.getId(), savedEntity.getBudgetCode());
        
        return savedEntity.getId();
    }

    /**
     * 编辑年度总预算
     *
     * @param entity 年度总预算实体
     * @return 年度总预算ID
     */
    public String edit(CostAnnualBudgetEntity entity) {
        if (entity == null || !StringUtils.hasText(entity.getId())) {
            throw new IllegalArgumentException("年度总预算数据或ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + entity.getId());
        }

        // 验证是否可以修改
        if (!existingEntity.canModify()) {
            throw new IllegalArgumentException("当前状态不允许修改，状态: " + existingEntity.getBudgetStatus());
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证预算编号唯一性（排除自己）
        if (StringUtils.hasText(entity.getBudgetCode()) && !entity.getBudgetCode().equals(existingEntity.getBudgetCode())) {
            CostAnnualBudgetEntity duplicateEntity = costAnnualBudgetRepository.findByBudgetCode(entity.getBudgetCode());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(entity.getId())) {
                throw new IllegalArgumentException("预算编号已存在：" + entity.getBudgetCode());
            }
        }

        // 计算剩余金额
        entity.calculateRevenueRemainingAmount();
        entity.calculateDirectCostRemainingAmount();

        CostAnnualBudgetEntity updatedEntity = costAnnualBudgetRepository.updateById(entity);
        log.info("编辑年度总预算成功，ID: {}, 预算编号: {}", updatedEntity.getId(), updatedEntity.getBudgetCode());
        
        return updatedEntity.getId();
    }

    /**
     * 根据ID删除年度总预算
     *
     * @param id 年度总预算ID
     */
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(id);
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + id);
        }

        // 验证是否可以删除
        if (!existingEntity.canDelete()) {
            throw new IllegalArgumentException("当前状态不允许删除，状态: " + existingEntity.getBudgetStatus());
        }

        costAnnualBudgetRepository.deleteById(id);
        log.info("删除年度总预算成功，ID: {}, 预算编号: {}", id, existingEntity.getBudgetCode());
    }

    /**
     * 批量删除年度总预算
     *
     * @param ids 年度总预算ID列表，逗号分隔
     */
    public void deleteBatch(String ids) {
        if (!StringUtils.hasText(ids)) {
            throw new IllegalArgumentException("年度总预算ID列表不能为空");
        }

        List<String> idList = Arrays.asList(ids.split(","));
        
        // 验证每个记录是否可以删除
        List<CostAnnualBudgetEntity> entities = costAnnualBudgetRepository.findByIds(idList);
        for (CostAnnualBudgetEntity entity : entities) {
            if (!entity.canDelete()) {
                throw new IllegalArgumentException("预算编号 " + entity.getBudgetCode() + " 当前状态不允许删除，状态: " + entity.getBudgetStatus());
            }
        }

        costAnnualBudgetRepository.deleteByIds(idList);
        log.info("批量删除年度总预算成功，删除数量: {}", idList.size());
    }

    /**
     * 生成下一个预算编号
     *
     * @return 下一个预算编号（ZYS+当前年份+3位流水）
     */
    public String generateNextBudgetCode() {
        // 获取当前年份
        String year = String.valueOf(java.time.Year.now().getValue());

        // 查询指定年份的最大预算编号
        String maxBudgetCode = costAnnualBudgetRepository.findMaxBudgetCodeByYear(year);

        int nextSequence = 1; // 默认从001开始

        if (StringUtils.hasText(maxBudgetCode)) {
            // 解析流水号：ZYS+4位年份+3位流水号
            String sequenceStr = maxBudgetCode.substring(7); // 去掉"ZYS"和4位年份
            try {
                int currentSequence = Integer.parseInt(sequenceStr);
                nextSequence = currentSequence + 1;
            } catch (NumberFormatException e) {
                log.warn("解析预算编号流水号失败，使用默认值001，预算编号: {}", maxBudgetCode);
            }
        }

        // 生成新的预算编号：ZYS+4位年份+3位流水号（补零）
        String nextBudgetCode = String.format("ZYS%s%03d", year, nextSequence);

        log.info("生成下一个预算编号成功，年份: {}, 预算编号: {}", year, nextBudgetCode);
        return nextBudgetCode;
    }

    /**
     * 保存年度预算（第一步）
     * 保存年度总预算主表信息、项目年度预算信息和直接成本明细
     *
     * @param mainBudget 年度总预算主表信息
     * @param budgetDetailList 项目年度预算明细列表
     * @return 年度总预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveStep1(CostAnnualBudgetEntity mainBudget, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (mainBudget == null) {
            throw new IllegalArgumentException("年度总预算数据不能为空");
        }
        // 验证必填字段
        validateRequiredFields(mainBudget);

        // 验证预算编号唯一性
        if (StringUtils.hasText(mainBudget.getBudgetCode())) {
            CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findByBudgetCode(mainBudget.getBudgetCode());
            if (existingEntity != null) {
                throw new IllegalArgumentException("预算编号已存在：" + mainBudget.getBudgetCode());
            }
        }

        // 计算剩余金额
        mainBudget.calculateRevenueRemainingAmount();
        mainBudget.calculateDirectCostRemainingAmount();

        // 保存主表
        CostAnnualBudgetEntity savedEntity = costAnnualBudgetRepository.save(mainBudget);

        // 处理明细数据（设置关联关系）
        if (budgetDetailList != null && !budgetDetailList.isEmpty()) {
            log.info("开始处理 {} 条明细数据", budgetDetailList.size());
            processDetailData(savedEntity.getId(), budgetDetailList);

        }

        log.info("保存年度预算第一步成功，版本名称: {}, ID: {}", mainBudget.getVersion(), savedEntity.getId());
        return savedEntity.getId();
    }

    /**
     * 编辑年度预算（第一步）
     * 编辑年度总预算主表信息、项目年度预算信息和直接成本明细
     * 采用"先删除再新增"的方式更新明细数据
     *
     * @param mainBudget 年度总预算主表信息
     * @param budgetDetailList 项目年度预算明细列表
     * @return 年度总预算ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String editStep1(CostAnnualBudgetEntity mainBudget, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (mainBudget == null || !StringUtils.hasText(mainBudget.getId())) {
            throw new IllegalArgumentException("年度总预算数据或ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(mainBudget.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + mainBudget.getId());
        }

        // 验证是否可以修改
        if (!existingEntity.canModify()) {
            throw new IllegalArgumentException("当前状态不允许修改，状态: " + existingEntity.getBudgetStatus());
        }

        // 验证必填字段
        validateRequiredFields(mainBudget);

        // 验证预算编号唯一性（排除自己）
        if (StringUtils.hasText(mainBudget.getBudgetCode()) && !mainBudget.getBudgetCode().equals(existingEntity.getBudgetCode())) {
            CostAnnualBudgetEntity duplicateEntity = costAnnualBudgetRepository.findByBudgetCode(mainBudget.getBudgetCode());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(mainBudget.getId())) {
                throw new IllegalArgumentException("预算编号已存在：" + mainBudget.getBudgetCode());
            }
        }

        // 计算剩余金额
        mainBudget.calculateRevenueRemainingAmount();
        mainBudget.calculateDirectCostRemainingAmount();

        // 更新主表
        CostAnnualBudgetEntity updatedEntity = costAnnualBudgetRepository.updateById(mainBudget);

        // 处理明细数据：先删除再新增
        if (budgetDetailList != null && !budgetDetailList.isEmpty()) {
            log.info("开始处理明细数据更新，先删除旧数据，再新增新数据，明细数量: {}", budgetDetailList.size());

            // 1. 先删除现有的明细数据
            costAnnualBudgetDetailRepository.deleteByBudgetId(mainBudget.getId());
            log.info("删除旧明细数据完成，budgetId: {}", mainBudget.getId());

            // 2. 重新保存新的明细数据
            processDetailData(mainBudget.getId(), budgetDetailList);
            log.info("保存新明细数据完成，budgetId: {}", mainBudget.getId());
        } else {
            // 如果没有明细数据，则删除所有现有明细数据
            log.info("没有新明细数据，删除所有现有明细数据，budgetId: {}", mainBudget.getId());
            costAnnualBudgetDetailRepository.deleteByBudgetId(mainBudget.getId());
        }

        log.info("编辑年度预算第一步成功，版本名称: {}, ID: {}", mainBudget.getVersion(), updatedEntity.getId());
        return updatedEntity.getId();
    }

    /**
     * 处理明细数据（设置关联关系和保存到数据库）
     */
    private void processDetailData(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            log.info("没有明细数据需要处理，budgetId: {}", budgetId);
            return;
        }

        log.info("开始处理明细数据，budgetId: {}, 明细数量: {}", budgetId, budgetDetailList.size());

        // 设置关联关系
        for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
            // 设置关联的预算主表ID
            detailInfo.setBudgetId(budgetId);

            // 处理直接成本明细
            if (detailInfo.getDirectCostList() != null && !detailInfo.getDirectCostList().isEmpty()) {
                for (CostAnnualBudgetEntity.DirectCostInfo directCostInfo : detailInfo.getDirectCostList()) {
                    // 设置关联的明细ID
                    directCostInfo.setBudgetDetailId(detailInfo.getId());
                }
            }
        }

        // 调用领域服务保存明细数据
        annualBudgetDetailDomainService.saveBudgetDetails(budgetId, budgetDetailList);

        log.info("明细数据处理完成，budgetId: {}", budgetId);
    }



    /**
     * 根据项目计划ID查询关联的预算科目信息
     *
     * @param projectPlanId 项目计划ID（可选参数）
     * @return 预算科目信息列表
     */
    public List<BudgetSubjectInfo> queryBudgetSubjects(String projectPlanId) {
        log.info("开始查询预算科目信息，项目计划ID: {}", projectPlanId);

        // 查询所有启用状态的预算科目
        List<CostBudgetSubjectEntity> subjectList = costBudgetSubjectRepository.findAllEnabled();
        List<BudgetSubjectInfo> resultList = new ArrayList<>();

        // 如果有项目计划ID，查询项目计划的直接成本总计
        BigDecimal directCostTotal = null;
        if (StringUtils.hasText(projectPlanId)) {
            try {
                CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(projectPlanId);
                if (projectPlan != null) {
                    directCostTotal = projectPlan.getDirectCostTotal();
                    log.info("查询到项目计划直接成本总计: {}", directCostTotal);
                } else {
                    log.warn("项目计划不存在，ID: {}", projectPlanId);
                }
            } catch (Exception e) {
                log.error("查询项目计划失败，ID: {}", projectPlanId, e);
            }
        }

        // 转换预算科目信息
        for (CostBudgetSubjectEntity subject : subjectList) {
            BudgetSubjectInfo info = new BudgetSubjectInfo();
            info.setSubjectCode(subject.getSubjectCode());
            info.setSubjectName(subject.getSubjectName());
            info.setSubjectDescription(subject.getSubjectDescription());
            info.setSortOrder(subject.getSortOrder());

            // 如果是"原材料及主要材料"科目且有项目计划ID，则设置预算金额
            if (directCostTotal != null && isRawMaterialSubject(subject)) {
                // directCostTotal单位是万元，需要转换为元：万元 × 10000 = 元
                BigDecimal budgetAmountInYuan = directCostTotal.multiply(new BigDecimal("10000"))
                    .setScale(2, java.math.RoundingMode.HALF_UP);
                info.setBudgetAmount(budgetAmountInYuan);
                log.info("为原材料科目设置预算金额: {} 万元 -> {} 元, 科目: {}",
                        directCostTotal, budgetAmountInYuan, subject.getSubjectName());
            } else {
                info.setBudgetAmount(BigDecimal.ZERO);
            }

            resultList.add(info);
        }

        log.info("查询预算科目信息完成，返回 {} 条记录", resultList.size());
        return resultList;
    }

    /**
     * 判断是否为原材料及主要材料科目
     *
     * @param subject 预算科目实体
     * @return 是否为原材料科目
     */
    private boolean isRawMaterialSubject(CostBudgetSubjectEntity subject) {
        if (subject == null || !StringUtils.hasText(subject.getSubjectName())) {
            return false;
        }

        String subjectName = subject.getSubjectName().trim();
        // 匹配"原材料及主要材料"或类似名称
        return subjectName.contains("原材料") && subjectName.contains("主要材料");
    }

    /**
     * 预算科目信息内部类
     */
    public static class BudgetSubjectInfo {
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal budgetAmount;
        private Integer sortOrder;

        // Getters and Setters
        public String getSubjectCode() { return subjectCode; }
        public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }

        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }

        public String getSubjectDescription() { return subjectDescription; }
        public void setSubjectDescription(String subjectDescription) { this.subjectDescription = subjectDescription; }

        public BigDecimal getBudgetAmount() { return budgetAmount; }
        public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }

        public Integer getSortOrder() { return sortOrder; }
        public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(CostAnnualBudgetEntity entity) {
        if (!StringUtils.hasText(entity.getBudgetYear())) {
            throw new IllegalArgumentException("年份不能为空");
        }
        if (!StringUtils.hasText(entity.getProfessionalCompany())) {
            throw new IllegalArgumentException("所属单位不能为空");
        }
    }

    /**
     * 保存中心间接成本导入数据
     * 封装对Infrastructure层的访问，避免Performance层直接依赖Infrastructure层
     *
     * @param importDataList 导入数据列表
     * @return 保存成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveCenterCostImportData(List<CenterCostImportDTO> importDataList) {
        if (importDataList == null || importDataList.isEmpty()) {
            log.warn("导入数据列表为空");
            return 0;
        }

        log.info("开始保存中心间接成本导入数据，数量: {}", importDataList.size());

        // 通过Repository接口保存数据，避免直接依赖Infrastructure层
        int savedCount = centerCostImportRepository.saveBatch(importDataList);

        log.info("中心间接成本导入数据保存完成，数量: {}", savedCount);
        return savedCount;
    }
}
