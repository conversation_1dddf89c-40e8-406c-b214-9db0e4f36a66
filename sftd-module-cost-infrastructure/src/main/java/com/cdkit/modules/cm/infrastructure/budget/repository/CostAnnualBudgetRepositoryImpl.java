package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetRepository;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudget;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 年度总预算仓储实现
 * <AUTHOR>
 * @date 2025-07-30
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CostAnnualBudgetRepositoryImpl implements CostAnnualBudgetRepository {

    private final CostAnnualBudgetMapper costAnnualBudgetMapper;

    @Override
    public CostAnnualBudgetEntity save(CostAnnualBudgetEntity entity) {
        CostAnnualBudget po = convertToPO(entity);
        costAnnualBudgetMapper.insert(po);
        entity.setId(po.getId());
        return entity;
    }

    @Override
    public CostAnnualBudgetEntity updateById(CostAnnualBudgetEntity entity) {
        CostAnnualBudget po = convertToPO(entity);
        costAnnualBudgetMapper.updateById(po);
        return entity;
    }

    @Override
    public void deleteById(String id) {
        costAnnualBudgetMapper.deleteById(id);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        costAnnualBudgetMapper.deleteBatchIds(ids);
    }

    @Override
    public CostAnnualBudgetEntity findById(String id) {
        CostAnnualBudget po = costAnnualBudgetMapper.selectById(id);
        return po != null ? convertToEntity(po) : null;
    }

    @Override
    public List<CostAnnualBudgetEntity> findByIds(List<String> ids) {
        List<CostAnnualBudget> poList = costAnnualBudgetMapper.selectBatchIds(ids);
        return poList.stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public PageRes<CostAnnualBudgetEntity> queryPageList(CostAnnualBudgetEntity queryEntity, PageReq pageReq) {
        // 构建查询条件
        LambdaQueryWrapper<CostAnnualBudget> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(queryEntity.getBudgetCode())) {
            queryWrapper.like(CostAnnualBudget::getBudgetCode, queryEntity.getBudgetCode());
        }
        if (StringUtils.hasText(queryEntity.getBudgetYear())) {
            queryWrapper.eq(CostAnnualBudget::getBudgetYear, queryEntity.getBudgetYear());
        }
        if (StringUtils.hasText(queryEntity.getBudgetStatus())) {
            queryWrapper.eq(CostAnnualBudget::getBudgetStatus, queryEntity.getBudgetStatus());
        }
        if (StringUtils.hasText(queryEntity.getProfessionalCompany())) {
            queryWrapper.like(CostAnnualBudget::getProfessionalCompany, queryEntity.getProfessionalCompany());
        }

        // 默认按创建时间倒序
        queryWrapper.orderByDesc(CostAnnualBudget::getCreateTime);

        // 分页查询
        Page<CostAnnualBudget> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());
        IPage<CostAnnualBudget> pageResult = costAnnualBudgetMapper.selectPage(page, queryWrapper);

        // 转换结果
        List<CostAnnualBudgetEntity> entityList = pageResult.getRecords().stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());

        return PageRes.of(pageResult.getCurrent(), pageResult.getSize(), entityList, pageResult.getTotal(), pageResult.getPages());
    }

    @Override
    public CostAnnualBudgetEntity findByBudgetCode(String budgetCode) {
        LambdaQueryWrapper<CostAnnualBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAnnualBudget::getBudgetCode, budgetCode);
        CostAnnualBudget po = costAnnualBudgetMapper.selectOne(queryWrapper);
        return po != null ? convertToEntity(po) : null;
    }

    @Override
    public List<CostAnnualBudgetEntity> findByBudgetYear(String budgetYear) {
        LambdaQueryWrapper<CostAnnualBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAnnualBudget::getBudgetYear, budgetYear);
        List<CostAnnualBudget> poList = costAnnualBudgetMapper.selectList(queryWrapper);
        return poList.stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public List<CostAnnualBudgetEntity> findByBudgetStatus(String budgetStatus) {
        LambdaQueryWrapper<CostAnnualBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAnnualBudget::getBudgetStatus, budgetStatus);
        List<CostAnnualBudget> poList = costAnnualBudgetMapper.selectList(queryWrapper);
        return poList.stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    @Override
    public String findMaxBudgetCodeByYear(String year) {
        LambdaQueryWrapper<CostAnnualBudget> queryWrapper = new LambdaQueryWrapper<>();
        // 查询指定年份的预算编号，格式：ZYS+年份+流水号
        queryWrapper.likeRight(CostAnnualBudget::getBudgetCode, "ZYS" + year)
                   .orderByDesc(CostAnnualBudget::getBudgetCode)
                   .last("LIMIT 1");

        CostAnnualBudget po = costAnnualBudgetMapper.selectOne(queryWrapper);
        return po != null ? po.getBudgetCode() : null;
    }

    @Override
    public PageRes<CostAnnualBudgetEntity> page(CostAnnualBudgetEntity queryEntity, PageReq pageReq) {
        return queryPageList(queryEntity, pageReq);
    }

    /**
     * 领域实体转换为持久化对象
     */
    private CostAnnualBudget convertToPO(CostAnnualBudgetEntity entity) {
        CostAnnualBudget po = new CostAnnualBudget();
        BeanUtils.copyProperties(entity, po);
        return po;
    }

    /**
     * 持久化对象转换为领域实体
     */
    private CostAnnualBudgetEntity convertToEntity(CostAnnualBudget po) {
        CostAnnualBudgetEntity entity = new CostAnnualBudgetEntity();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
