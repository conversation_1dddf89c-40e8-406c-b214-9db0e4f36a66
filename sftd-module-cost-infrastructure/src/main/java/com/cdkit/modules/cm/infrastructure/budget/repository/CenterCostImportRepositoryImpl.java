package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;
import com.cdkit.modules.cm.domain.budget.repository.CenterCostImportRepository;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetCenterCostImport;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetCenterCostImportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 中心间接成本导入数据仓储实现
 * 实现Domain层定义的Repository接口，封装对Infrastructure层的访问
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CenterCostImportRepositoryImpl implements CenterCostImportRepository {

    private final ICostAnnualBudgetCenterCostImportService centerCostImportService;

    @Override
    public int saveBatch(List<CenterCostImportDTO> importDataList) {
        if (importDataList == null || importDataList.isEmpty()) {
            log.warn("导入数据列表为空");
            return 0;
        }

        log.info("开始批量保存中心间接成本导入数据，数量: {}", importDataList.size());

        // 转换DTO为Infrastructure层的Entity
        List<CostAnnualBudgetCenterCostImport> entityList = importDataList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());

        // 调用Infrastructure层的Service进行批量保存
        boolean success = centerCostImportService.saveBatch(entityList);
        
        if (success) {
            log.info("批量保存中心间接成本导入数据成功，数量: {}", entityList.size());
            return entityList.size();
        } else {
            log.error("批量保存中心间接成本导入数据失败");
            return 0;
        }
    }

    @Override
    public int deleteByBudgetCode(String budgetCode) {
        // TODO: 实现根据预算编号删除的逻辑
        log.info("删除预算编号为 {} 的导入数据", budgetCode);
        return 0;
    }

    @Override
    public List<CenterCostImportDTO> findByBudgetCode(String budgetCode) {
        // TODO: 实现根据预算编号查询的逻辑
        log.info("查询预算编号为 {} 的导入数据", budgetCode);
        return new ArrayList<>();
    }

    /**
     * 将DTO转换为Infrastructure层的Entity
     * 
     * @param dto 领域传输对象
     * @return Infrastructure层实体
     */
    private CostAnnualBudgetCenterCostImport convertToEntity(CenterCostImportDTO dto) {
        CostAnnualBudgetCenterCostImport entity = new CostAnnualBudgetCenterCostImport();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 将Infrastructure层的Entity转换为DTO
     * 
     * @param entity Infrastructure层实体
     * @return 领域传输对象
     */
    private CenterCostImportDTO convertToDTO(CostAnnualBudgetCenterCostImport entity) {
        CenterCostImportDTO dto = new CenterCostImportDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}
