package com.cdkit.modules.cm.infrastructure.budget.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: 年度总预算
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Schema(description="cost_annual_budget对象")
@Data
@TableName("cost_annual_budget")
public class CostAnnualBudget implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**总预算编号(ZYS+4位年份+3位流水)*/
	@Excel(name = "总预算编号(ZYS+4位年份+3位流水)", width = 15)
    @Schema(description = "总预算编号(ZYS+4位年份+3位流水)")
    private String budgetCode;
	/**年份*/
	@Excel(name = "年份", width = 15)
    @Schema(description = "年份")
    private String budgetYear;
	/**版本号*/
	@Excel(name = "版本号", width = 15)
    @Schema(description = "版本号")
    private String version;
	/**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)*/
	@Excel(name = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)", width = 15, dicCode = "cost_budget_status")
    @Dict(dicCode = "cost_budget_status")
    @Schema(description = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)")
    private String budgetStatus;
	/**所属单位*/
	@Excel(name = "所属单位", width = 15)
    @Schema(description = "所属单位")
    private String professionalCompany;
	/**收入总金额(万元)*/
	@Excel(name = "收入总金额(万元)", width = 15)
    @Schema(description = "收入总金额(万元)")
    private java.math.BigDecimal revenueTotalAmount;
	/**收入已使用金额(万元)*/
	@Excel(name = "收入已使用金额(万元)", width = 15)
    @Schema(description = "收入已使用金额(万元)")
    private java.math.BigDecimal revenueUsedAmount;
	/**收入剩余金额(万元)*/
	@Excel(name = "收入剩余金额(万元)", width = 15)
    @Schema(description = "收入剩余金额(万元)")
    private java.math.BigDecimal revenueRemainingAmount;
	/**收入总金额差异额(万元)*/
	@Excel(name = "收入总金额差异额(万元)", width = 15)
    @Schema(description = "收入总金额差异额(万元)")
    private java.math.BigDecimal revenueDifferenceAmount;
	/**直接成本总金额(万元)*/
	@Excel(name = "直接成本总金额(万元)", width = 15)
    @Schema(description = "直接成本总金额(万元)")
    private java.math.BigDecimal directCostTotalAmount;
	/**直接成本已使用金额(万元)*/
	@Excel(name = "直接成本已使用金额(万元)", width = 15)
    @Schema(description = "直接成本已使用金额(万元)")
    private java.math.BigDecimal directCostUsedAmount;
	/**直接成本剩余金额(万元)*/
	@Excel(name = "直接成本剩余金额(万元)", width = 15)
    @Schema(description = "直接成本剩余金额(万元)")
    private java.math.BigDecimal directCostRemainingAmount;
	/**直接成本总金额(万元)*/
	@Excel(name = "直接成本总金额(万元)", width = 15)
    @Schema(description = "直接成本总金额(万元)")
    private java.math.BigDecimal directCostTotalBudget;
	/**提交时间*/
	@Excel(name = "提交时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "提交时间")
    private Date submitTime;
	/**提交人*/
	@Excel(name = "提交人", width = 15)
    @Schema(description = "提交人")
    private String submitBy;
	/**审批时间*/
	@Excel(name = "审批时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审批时间")
    private Date approveTime;
	/**审批人*/
	@Excel(name = "审批人", width = 15)
    @Schema(description = "审批人")
    private String approveBy;
	/**审批备注*/
	@Excel(name = "审批备注", width = 15)
    @Schema(description = "审批备注")
    private String approveRemark;
	/**父预算ID(变更时关联原预算)*/
	@Excel(name = "父预算ID(变更时关联原预算)", width = 15)
    @Schema(description = "父预算ID(变更时关联原预算)")
    private String parentBudgetId;
	/**变更原因*/
	@Excel(name = "变更原因", width = 15)
    @Schema(description = "变更原因")
    private String changeReason;
	/**附件URL*/
	@Excel(name = "附件URL", width = 15)
    @Schema(description = "附件URL")
    private String attachmentUrl;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
