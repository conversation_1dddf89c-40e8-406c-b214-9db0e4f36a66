package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;

import java.util.List;

/**
 * 年度预算明细仓储接口
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface CostAnnualBudgetDetailRepository {

    /**
     * 保存年度预算明细数据
     * 
     * @param budgetId 年度预算主表ID
     * @param budgetDetailList 明细数据列表
     */
    void saveBudgetDetails(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList);

    /**
     * 根据预算ID查询明细数据
     * 
     * @param budgetId 年度预算主表ID
     * @return 明细数据列表
     */
    List<CostAnnualBudgetEntity.BudgetDetailInfo> findByBudgetId(String budgetId);

    /**
     * 删除年度预算明细数据
     * 
     * @param budgetId 年度预算主表ID
     */
    void deleteByBudgetId(String budgetId);
}
