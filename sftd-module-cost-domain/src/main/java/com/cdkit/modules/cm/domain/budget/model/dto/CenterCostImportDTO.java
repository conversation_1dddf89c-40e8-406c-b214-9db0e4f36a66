package com.cdkit.modules.cm.domain.budget.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 中心间接成本导入数据传输对象
 * 用于封装从Performance层传递到Application层的导入数据
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "中心间接成本导入数据传输对象")
public class CenterCostImportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总预算编号(关联年度总预算)
     */
    @Schema(description = "总预算编号(关联年度总预算)")
    private String budgetCode;

    /**
     * 中心名称
     */
    @Schema(description = "中心名称")
    private String center;

    /**
     * 模版类型
     * this_center_indirect_cost_template: 本中心间接成本导入模板
     * non_operational_center_indirect_cost_template: 非经营中心间接成本导入模板
     * general_admin_indirect_cost_template: 综合管理间接成本导入模板
     */
    @Schema(description = "模版类型")
    private String templateType;

    /**
     * 预算科目编码
     */
    @Schema(description = "预算科目编码")
    private String subjectCode;

    /**
     * 预算科目名称
     */
    @Schema(description = "预算科目名称")
    private String subjectName;

    /**
     * 成本金额(万元)
     */
    @Schema(description = "成本金额(万元)")
    private BigDecimal costAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
