package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.modules.cm.domain.budget.model.dto.CenterCostImportDTO;

import java.util.List;

/**
 * 中心间接成本导入数据仓储接口
 * 定义中心间接成本导入数据的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface CenterCostImportRepository {

    /**
     * 批量保存中心间接成本导入数据
     * 
     * @param importDataList 导入数据列表
     * @return 保存成功的数量
     */
    int saveBatch(List<CenterCostImportDTO> importDataList);

    /**
     * 根据预算编号删除导入数据
     * 
     * @param budgetCode 预算编号
     * @return 删除的数量
     */
    int deleteByBudgetCode(String budgetCode);

    /**
     * 根据预算编号查询导入数据
     * 
     * @param budgetCode 预算编号
     * @return 导入数据列表
     */
    List<CenterCostImportDTO> findByBudgetCode(String budgetCode);
}
